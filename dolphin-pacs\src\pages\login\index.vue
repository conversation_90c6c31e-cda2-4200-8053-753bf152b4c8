<script lang="ts" setup>
import type { FormRules } from "element-plus"
import type { LoginRequestData } from "./apis/type"
import { Lock, User } from "@element-plus/icons-vue"
import { useUserStore } from "@/pinia/stores/user"
import { loginApi } from "./apis"

const router = useRouter()

const userStore = useUserStore()

/** 登录表单元素的引用 */
const loginFormRef = useTemplateRef("loginFormRef")

/** 登录按钮 Loading */
const loading = ref(false)

/** 记住密码 */
const rememberMe = ref(false)

/** 登录表单数据 */
const loginFormData: LoginRequestData = reactive({
  username: "dolphin",
  password: "123456"
})

/** 登录表单校验规则 */
const loginFormRules: FormRules = {
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" }
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, max: 16, message: "长度在 8 到 16 个字符", trigger: "blur" }
  ]
}

/** 登录 */
function handleLogin() {
  loginFormRef.value?.validate((valid: boolean) => {
    if (!valid) {
      ElMessage.error("表单校验不通过")
      return
    }
    loading.value = true
    loginApi(loginFormData).then(({ data }) => {
      userStore.setToken(data.accessToken)
      userStore.setRefreshToken(data.refreshToken)

      // 如果后端返回了用户信息，直接存储（可选）
      if (data.username) {
        userStore.username = data.username
      }
      if (data.roles && data.roles.length > 0) {
        userStore.roles = data.roles
      }

      router.push("/")
    }).catch(() => {
      loginFormData.password = ""
    }).finally(() => {
      loading.value = false
    })
  })
}
</script>

<template>
  <div class="login-page">
    <div class="left-section">
      <div class="illustration-container">
        <div class="character-illustration">
          <img src="@@/assets/images/logo/doctor.jpg" alt="登录页面插图" class="demo-image" />
        </div>
      </div>
    </div>

    <div class="right-section">
      <div class="login-form-container">
        <div class="brand-section">
          <div class="brand-logo">
            <img src="@@/assets/images/logo/logo.png" alt="Dolphin AI Logo" class="logo-image" />
            <img src="@@/assets/images/logo/logo.svg" alt="Dolphin AI" class="logo-svg" />
          </div>
          <h1 class="brand-title">智影联PACS系统</h1>
          <p class="brand-subtitle">来自于海豚之声研发部，一群执着探索AI医疗的小分队</p>
        </div>

        <el-form ref="loginFormRef" :model="loginFormData" :rules="loginFormRules" class="login-form" @submit.prevent="handleLogin">
          <el-form-item prop="username" class="form-field">
            <el-input
              v-model="loginFormData.username"
              placeholder="请输入用户名"
              size="large"
              :prefix-icon="User"
              class="login-input"
            />
          </el-form-item>

          <el-form-item prop="password" class="form-field">
            <el-input
              v-model="loginFormData.password"
              type="password"
              placeholder="请输入密码"
              size="large"
              :prefix-icon="Lock"
              show-password
              class="login-input"
            />
          </el-form-item>

          <div class="remember-section">
            <el-checkbox v-model="rememberMe" class="remember-checkbox">
              <span class="checkbox-label">
                记住密码，同意《
                <span class="link-text">隐私政策</span>
                》和《
                <span class="link-text">用户协议</span>
                》
              </span>
            </el-checkbox>
          </div>

          <el-button type="primary" size="large" :loading="loading" class="login-btn" @click="handleLogin">登录</el-button>
        </el-form>
      </div>
    </div>
  </div>
</template>

<style scoped>
.login-page {
  display: flex;
  height: 100vh;
  width: 100%;
  /* 确保登录页面不受全局主题影响 */
  isolation: isolate;
  position: relative;
  z-index: 1;
}

/* 左侧插图区域 */
.left-section {
  flex: 0.7;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.illustration-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.character-illustration {
  position: relative;
  width: 100%;
  height: 110%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.demo-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0;
}

/* 右侧登录表单区域 */
.right-section {
  flex: 1;
  background: #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.login-form-container {
  width: 100%;
  max-width: 400px;
}

.brand-section {
  text-align: left;
  margin-bottom: 2rem;
}

.brand-logo {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.logo-image {
  width: 48px;
  height: 48px;
  margin-right: 0.75rem;
  object-fit: contain;
}

.logo-svg {
  height: 32px;
  width: auto;
  margin-left: 0.75rem;
  object-fit: contain;
  /* 在登录页面的暗色背景下增加亮度 */
  filter: brightness(1.2) contrast(1.1) saturate(1.1);
}

.brand-title {
  color: #ffffff;
  font-size: 2.5rem;
  font-weight: 600;
  margin: 0.5rem 0;
  margin-bottom: 1rem;
}

.brand-subtitle {
  color: #9ca3af;
  font-size: 0.9rem;
  font-weight: 400;
  margin: 0;
}

.login-form {
  width: 100%;
}

/* Element Plus 表单样式 */
.login-form {
  width: 100%;
}

.form-field {
  margin-bottom: 1.5rem;
}

/* Element Plus 输入框样式 */
.login-page .login-input :deep(.el-input__wrapper) {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  box-shadow: none !important;
}

.login-page .login-input :deep(.el-input__wrapper:hover) {
  border-color: rgba(255, 255, 255, 0.4) !important;
}

.login-page .login-input :deep(.el-input__wrapper.is-focus) {
  background: rgba(255, 255, 255, 0.08) !important;
  border-color: #4a90e2 !important;
  border-width: 2px !important;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2) !important;
}

.login-page .login-input :deep(.el-input__inner) {
  color: white !important;
  background: transparent !important;
}

.login-page .login-input :deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.5) !important;
}

.login-page .login-input :deep(.el-input__prefix-inner) {
  color: rgba(255, 255, 255, 0.7) !important;
}

.login-page .login-input :deep(.el-input__suffix-inner) {
  color: rgba(255, 255, 255, 0.7) !important;
}

/* 记住密码区域 */
.remember-section {
  margin-bottom: 2rem;
}

.remember-checkbox :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #4a90e2 !important;
  border-color: #4a90e2 !important;
}

.remember-checkbox :deep(.el-checkbox__inner) {
  border-color: rgba(255, 255, 255, 0.3) !important;
  background-color: transparent !important;
}

.remember-checkbox :deep(.el-checkbox__inner:hover) {
  border-color: #4a90e2 !important;
}

.checkbox-label {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
}

.link-text {
  color: #4a90e2;
  text-decoration: underline;
  cursor: pointer;
}

.link-text:hover {
  color: #357abd;
}

/* Element Plus 按钮样式 */
.login-btn {
  width: 100% !important;
  height: 48px !important;
  background: #4a90e2 !important;
  border-color: #4a90e2 !important;
  border-radius: 8px !important;
  font-size: 1rem !important;
  font-weight: 500 !important;
  box-shadow: 0 2px 4px rgba(74, 144, 226, 0.2) !important;
}

.login-btn:hover {
  background: #357abd !important;
  border-color: #357abd !important;
  box-shadow: 0 4px 8px rgba(74, 144, 226, 0.3) !important;
}

.login-btn:focus {
  background: #4a90e2 !important;
  border-color: #4a90e2 !important;
  box-shadow: 0 2px 4px rgba(74, 144, 226, 0.2), 0 0 0 2px rgba(74, 144, 226, 0.3) !important;
}

.login-btn.is-loading {
  background: rgba(74, 144, 226, 0.8) !important;
  border-color: rgba(74, 144, 226, 0.8) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-page {
    flex-direction: column;
  }

  .left-section {
    height: 40vh;
  }

  .right-section {
    height: 60vh;
    padding: 1rem;
  }

  .demo-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}
</style>
