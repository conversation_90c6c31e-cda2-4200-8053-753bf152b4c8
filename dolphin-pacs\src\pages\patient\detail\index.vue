<script lang="ts" setup>
import { ref, onMounted } from "vue"
import { useRoute } from "vue-router"
import { ElMessage } from "element-plus"
import type { PatientInfo } from "../management/types"
import { getPatientInfoApi } from "@/common/apis/patients"
import { transformPatientData } from "../management/utils/dataTransform"
import ChatSidebar from "./components/ChatSidebar.vue"

defineOptions({
  name: "PatientDetail"
})

const route = useRoute()

// 患者详情数据
const patientDetail = ref<PatientInfo | null>(null)
const loading = ref(false)

// 当前激活的标签页
const activeTab = ref('medical-record')

// 患者信息区域收缩状态
const isPatientInfoCollapsed = ref(false)

// 获取患者ID
const patientId = route.params.id as string

// 获取患者详情数据
const fetchPatientDetail = async () => {
  if (!patientId) {
    ElMessage.error('患者ID不能为空')
    return
  }

  loading.value = true
  try {
    const response = await getPatientInfoApi(patientId)
    console.log('API响应:', response) // 调试日志
    if (response.data) {
      console.log('患者数据:', response.data) // 调试日志
      patientDetail.value = transformPatientData(response.data)
      console.log('转换后的数据:', patientDetail.value) // 调试日志
    } else {
      console.log('数据结构异常:', response) // 调试日志
      ElMessage.error('获取患者详情失败')
    }
  } catch (error) {
    console.error('获取患者详情失败:', error)
    ElMessage.error('获取患者详情失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchPatientDetail()
})
</script>

<template>
  <div class="patient-detail">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="8" animated />
    </div>

    <!-- 患者详情内容 -->
    <div v-else-if="patientDetail" class="detail-content">
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 患者信息区域 -->
        <div class="patient-info-section" :class="{ 'collapsed': isPatientInfoCollapsed }">
          <!-- 收缩控制头部 -->
          <div class="info-header" @click="isPatientInfoCollapsed = !isPatientInfoCollapsed">
            <div class="header-left">
              <h3 class="section-title">患者信息</h3>
              <div class="quick-info" v-if="isPatientInfoCollapsed">
                <span class="quick-item">{{ patientDetail.name }}</span>
                <span class="quick-item">{{ patientDetail.gender }}</span>
                <span class="quick-item">{{ patientDetail.age }}岁</span>
                <span class="quick-item">{{ patientDetail.department }}</span>
              </div>
            </div>
            <div class="header-right">
              <span class="collapse-icon" :class="{ 'rotated': isPatientInfoCollapsed }">
                ▼
              </span>
            </div>
          </div>

          <!-- 详细信息内容 -->
          <el-collapse-transition>
            <div v-show="!isPatientInfoCollapsed" class="info-content">
              <!-- 第一行：基本信息 -->
              <el-row :gutter="16" class="info-row">
                <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                  <div class="field-card">
                    <div class="field-label">姓名</div>
                    <div class="field-value">{{ patientDetail.name || '' }}</div>
                  </div>
                </el-col>
                <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                  <div class="field-card">
                    <div class="field-label">性别</div>
                    <div class="field-value">{{ patientDetail.gender || '' }}</div>
                  </div>
                </el-col>
                <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                  <div class="field-card">
                    <div class="field-label">年龄</div>
                    <div class="field-value">{{ patientDetail.age ? patientDetail.age + '岁' : '' }}</div>
                  </div>
                </el-col>
                <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                  <div class="field-card">
                    <div class="field-label">病案号</div>
                    <div class="field-value"></div>
                  </div>
                </el-col>
                <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                  <div class="field-card">
                    <div class="field-label">病人ID</div>
                    <div class="field-value">{{ patientDetail.id || '' }}</div>
                  </div>
                </el-col>
                <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                  <div class="field-card">
                    <div class="field-label">科室</div>
                    <div class="field-value">{{ patientDetail.department || '' }}</div>
                  </div>
                </el-col>
              </el-row>

              <!-- 第二行：检查信息 -->
              <el-row :gutter="16" class="info-row">
                <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                  <div class="field-card">
                    <div class="field-label">床号</div>
                    <div class="field-value"></div>
                  </div>
                </el-col>
                <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                  <div class="field-card">
                    <div class="field-label">仪器</div>
                    <div class="field-value"></div>
                  </div>
                </el-col>
                <el-col :xl="4" :lg="6" :md="8" :sm="12" :xs="24">
                  <div class="field-card">
                    <div class="field-label">检查机号</div>
                    <div class="field-value"></div>
                  </div>
                </el-col>
                <el-col :xl="12" :lg="18" :md="16" :sm="24" :xs="24">
                  <div class="field-card">
                    <div class="field-label">检查项目</div>
                    <div class="field-value">{{ patientDetail.examType || '' }}</div>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-collapse-transition>
        </div>

        <!-- 标签页区域 -->
        <div class="tabs-section">
          <el-tabs v-model="activeTab" class="detail-tabs">
            <el-tab-pane label="患者病历" name="medical-record">
              <div class="tab-content">
                <!-- 患者病历内容区域 -->
                <div class="content-placeholder">
                  患者病历内容区域
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="检查报告" name="examination-report">
              <div class="tab-content">
                <!-- 检查报告内容区域 -->
                <div class="content-placeholder">
                  检查报告内容区域
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="超声视频" name="ultrasound-video">
              <div class="tab-content">
                <!-- 超声视频内容区域 -->
                <div class="content-placeholder">
                  超声视频内容区域
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane label="超声图像" name="ultrasound-image">
              <div class="tab-content">
                <!-- 超声图像内容区域 -->
                <div class="content-placeholder">
                  超声图像内容区域
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <!-- 右侧聊天区域 -->
      <ChatSidebar />
    </div>

    <!-- 数据为空状态 -->
    <div v-else class="empty-state">
      <el-empty description="未找到患者信息" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.patient-detail {
  padding: 20px;
  min-height: calc(100vh - 84px);
  background-color: var(--el-bg-color-page);

  .loading-container {
    background: var(--el-bg-color);
    padding: 24px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .detail-content {
    display: flex;
    gap: 20px;
    height: calc(100vh - 120px);

    // 主要内容区域
    .main-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      // 患者信息区域
      .patient-info-section {
        border-radius: 8px;
        margin-bottom: 20px;
        background: var(--el-bg-color);
        flex-shrink: 0;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;

        // 收缩控制头部
        .info-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 16px 20px;
          background: var(--el-color-primary-light-9);
          border-radius: 8px 8px 0 0;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background: var(--el-color-primary-light-8);
          }

          .header-left {
            display: flex;
            align-items: center;
            gap: 20px;

            .section-title {
              margin: 0;
              font-size: 16px;
              font-weight: 600;
              color: var(--el-color-primary);
            }

            .quick-info {
              display: flex;
              align-items: center;
              gap: 16px;

              .quick-item {
                font-size: 14px;
                color: var(--el-text-color-regular);
                background: var(--el-bg-color);
                padding: 4px 8px;
                border-radius: 4px;
                border: 1px solid var(--el-border-color-light);
              }
            }
          }

          .header-right {
            .collapse-icon {
              font-size: 14px;
              color: var(--el-color-primary);
              transition: transform 0.3s ease;

              &.rotated {
                transform: rotate(-90deg);
              }
            }
          }
        }

        // 收缩状态样式
        &.collapsed {
          .info-header {
            border-radius: 8px;
          }
        }

        // 详细信息内容
        .info-content {
          padding: 20px;

          .info-row {
            margin-bottom: 16px;

            &:last-child {
              margin-bottom: 0;
            }
          }

          .field-card {
            background: var(--el-bg-color);
            border: 1px solid var(--el-border-color-light);
            border-radius: 6px;
            padding: 12px 16px;
            height: 60px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            transition: all 0.3s ease;

            &:hover {
              border-color: var(--el-color-primary);
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            .field-label {
              font-size: 12px;
              color: var(--el-text-color-regular);
              margin-bottom: 4px;
              font-weight: 500;
            }

            .field-value {
              font-size: 14px;
              color: var(--el-text-color-primary);
              font-weight: 500;
              min-height: 20px;
              display: flex;
              align-items: center;
            }
          }
        }
      }

      // 标签页区域
      .tabs-section {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        background: var(--el-bg-color);
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .detail-tabs {
          height: 100%;
          display: flex;
          flex-direction: column;

          :deep(.el-tabs__header) {
            margin-bottom: 0;
            border-bottom: 1px solid var(--el-border-color-light);
            flex-shrink: 0;
            background: var(--el-bg-color);
            border-radius: 8px 8px 0 0;
          }

          :deep(.el-tabs__nav-wrap) {
            padding: 0 20px;
          }

          :deep(.el-tabs__item) {
            font-size: 16px;
            font-weight: 500;
            padding: 0 30px;
            height: 50px;
            line-height: 50px;
            border-radius: 8px 8px 0 0;
          }

          :deep(.el-tabs__content) {
            flex: 1;
            overflow: hidden;
            background: var(--el-bg-color);
          }

          .tab-content {
            padding: 0;
            height: 100%;
            background: var(--el-bg-color);
            overflow-y: auto;

            .content-placeholder {
              display: flex;
              align-items: center;
              justify-content: center;
              height: 100%;
              min-height: 500px;
              font-size: 16px;
              color: var(--el-text-color-regular);
              background: var(--el-bg-color);
              border: 1px solid var(--el-border-color-light);
              margin: 0;
            }
          }
        }
      }
    }


  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    background: var(--el-bg-color);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}

// 响应式设计
@media screen and (max-width: 768px) {
  .patient-detail {
    padding: 10px;

    .detail-content {
      flex-direction: column;
      height: auto;

      .main-content {
        .patient-info-section {
          margin-bottom: 15px;

          .info-header {
            padding: 12px 16px;

            .header-left {
              gap: 12px;

              .section-title {
                font-size: 14px;
              }

              .quick-info {
                gap: 8px;

                .quick-item {
                  font-size: 12px;
                  padding: 2px 6px;
                }
              }
            }
          }

          .info-content {
            padding: 15px;

            .info-row {
              margin-bottom: 12px;
            }

            .field-card {
              height: 50px;
              padding: 8px 12px;

              .field-label {
                font-size: 11px;
                margin-bottom: 2px;
              }

              .field-value {
                font-size: 13px;
              }
            }
          }
        }

        .tabs-section {
          .detail-tabs {
            :deep(.el-tabs__nav-wrap) {
              padding: 0 10px;
            }

            :deep(.el-tabs__item) {
              font-size: 14px;
              padding: 0 15px;
              height: 45px;
              line-height: 45px;
            }

            .tab-content {
              padding: 0;
              min-height: 400px;

              .content-placeholder {
                height: 100%;
                min-height: 350px;
                font-size: 14px;
                margin: 0;
              }
            }
          }
        }
      }


    }
  }
}
</style>
